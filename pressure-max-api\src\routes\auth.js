const express = require('express');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const crypto = require('crypto');
const nodemailer = require('nodemailer');

const authMiddleware = require('../middleware/auth');
const { asyncHand<PERSON>, ValidationError, AuthenticationError, NotFoundError } = require('../middleware/errorHandler');
const User = require('../models/User');
const redis = require('../config/redis');
const config = require('../config/config');
const logger = require('../config/logger');

const router = express.Router();

// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: {
    error: 'Too many authentication attempts',
    message: 'Please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Email transporter
const emailTransporter = nodemailer.createTransport(config.email);

/**
 * @swagger
 * /auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - firstName
 *               - lastName
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 8
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               phone:
 *                 type: string
 *               timezone:
 *                 type: string
 *     responses:
 *       201:
 *         description: User registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *                 tokens:
 *                   type: object
 *                   properties:
 *                     accessToken:
 *                       type: string
 *                     refreshToken:
 *                       type: string
 */
router.post('/register', [
  authLimiter,
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
  body('firstName').trim().isLength({ min: 1 }).withMessage('First name is required'),
  body('lastName').trim().isLength({ min: 1 }).withMessage('Last name is required'),
  body('phone').optional().isMobilePhone().withMessage('Valid phone number required'),
  body('timezone').optional().isString().withMessage('Valid timezone required')
], asyncHandler(async (req, res) => {
  // Check validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { email, password, firstName, lastName, phone, timezone } = req.body;

  // Check if user already exists
  const existingUser = await User.findByEmail(email);
  if (existingUser) {
    throw new ValidationError('User with this email already exists');
  }

  // Hash password
  const hashedPassword = await authMiddleware.hashPassword(password);

  // Create user
  const user = await User.create({
    email,
    password: hashedPassword,
    firstName,
    lastName,
    phone,
    timezone
  });

  // Generate tokens
  const tokenPayload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    tenantId: user.tenantId
  };

  const accessToken = authMiddleware.generateToken(tokenPayload);
  const refreshToken = authMiddleware.generateRefreshToken(tokenPayload);

  // Store refresh token
  await redis.set(`refresh_token:${user.id}`, refreshToken, 30 * 24 * 60 * 60); // 30 days

  // Send welcome email (async)
  sendWelcomeEmail(user).catch(error => {
    logger.error('Failed to send welcome email:', error);
  });

  res.status(201).json({
    message: 'User registered successfully',
    user: user.toJSON(),
    tokens: {
      accessToken,
      refreshToken
    }
  });
}));

/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: Login user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *                 tokens:
 *                   type: object
 *                   properties:
 *                     accessToken:
 *                       type: string
 *                     refreshToken:
 *                       type: string
 */
router.post('/login', [
  authLimiter,
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password').notEmpty().withMessage('Password is required')
], asyncHandler(async (req, res) => {
  // Check validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { email, password } = req.body;

  // Find user
  const user = await User.findByEmail(email);
  if (!user) {
    throw new AuthenticationError('Invalid email or password');
  }

  // Check if user is active
  if (!user.isActive) {
    throw new AuthenticationError('Account has been deactivated');
  }

  // Verify password
  const isValidPassword = await authMiddleware.comparePassword(password, user.password);
  if (!isValidPassword) {
    throw new AuthenticationError('Invalid email or password');
  }

  // Update last login
  await user.updateLastLogin();

  // Generate tokens
  const tokenPayload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    tenantId: user.tenantId
  };

  const accessToken = authMiddleware.generateToken(tokenPayload);
  const refreshToken = authMiddleware.generateRefreshToken(tokenPayload);

  // Store refresh token
  await redis.set(`refresh_token:${user.id}`, refreshToken, 30 * 24 * 60 * 60); // 30 days

  res.json({
    message: 'Login successful',
    user: user.toJSON(),
    tokens: {
      accessToken,
      refreshToken
    }
  });
}));

/**
 * @swagger
 * /auth/refresh:
 *   post:
 *     summary: Refresh access token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 */
router.post('/refresh', [
  body('refreshToken').notEmpty().withMessage('Refresh token is required')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('Validation failed', errors.array());
  }

  const { refreshToken } = req.body;

  // Verify refresh token
  const decoded = authMiddleware.verifyRefreshToken(refreshToken);

  // Check if refresh token exists in Redis
  const storedToken = await redis.get(`refresh_token:${decoded.userId}`);
  if (!storedToken || storedToken !== refreshToken) {
    throw new AuthenticationError('Invalid refresh token');
  }

  // Get user
  const user = await User.findById(decoded.userId);
  if (!user || !user.isActive) {
    throw new AuthenticationError('User not found or inactive');
  }

  // Generate new tokens
  const tokenPayload = {
    userId: user.id,
    email: user.email,
    role: user.role,
    tenantId: user.tenantId
  };

  const newAccessToken = authMiddleware.generateToken(tokenPayload);
  const newRefreshToken = authMiddleware.generateRefreshToken(tokenPayload);

  // Update stored refresh token
  await redis.set(`refresh_token:${user.id}`, newRefreshToken, 30 * 24 * 60 * 60);

  res.json({
    message: 'Token refreshed successfully',
    tokens: {
      accessToken: newAccessToken,
      refreshToken: newRefreshToken
    }
  });
}));

/**
 * @swagger
 * /auth/logout:
 *   post:
 *     summary: Logout user
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logout successful
 */
router.post('/logout', authMiddleware.authenticate, asyncHandler(async (req, res) => {
  const token = req.headers.authorization.substring(7);

  // Blacklist the access token
  await authMiddleware.blacklistToken(token);

  // Remove refresh token
  await redis.del(`refresh_token:${req.user.id}`);

  logger.audit('user_logout', req.user.id);

  res.json({
    message: 'Logout successful'
  });
}));

// Helper function to send welcome email
async function sendWelcomeEmail(user) {
  const mailOptions = {
    from: config.email.auth.user,
    to: user.email,
    subject: 'Welcome to Pressure Max!',
    html: `
      <h1>Welcome to Pressure Max, ${user.firstName}!</h1>
      <p>Thank you for joining our platform. You can now start creating and managing your Facebook ad campaigns.</p>
      <p>If you have any questions, please don't hesitate to contact our support team.</p>
      <p>Best regards,<br>The Pressure Max Team</p>
    `
  };

  await emailTransporter.sendMail(mailOptions);
}

module.exports = router;
