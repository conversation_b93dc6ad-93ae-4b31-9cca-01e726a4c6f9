{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:17'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:17'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:18'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:18'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:19'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:19'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:20'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:20'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:21'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:21'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:22'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:22'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:23'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:23'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:26'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:26'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:27'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:27'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:30'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:30'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:16:31'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 12:17:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:24:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:24'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:25:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:27:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:27:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:27:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:27:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:27:25'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:28:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:28:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:28:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:28:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:28:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:29:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:30:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:46:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-url?redirectUri=http://localhost:3001',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624',
  userId: undefined,
  body: {},
  params: {},
  query: { redirectUri: 'http://localhost:3001' },
  level: 'error',
  timestamp: '2025-06-25 17:30:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:46:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-url?redirectUri=http%3A%2F%2Flocalhost%3A3001',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624',
  userId: undefined,
  body: {},
  params: {},
  query: { redirectUri: 'http://localhost:3001' },
  level: 'error',
  timestamp: '2025-06-25 17:30:50'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:51:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-url?redirectUri=http://localhost:3001',
  method: 'GET',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.3624',
  userId: undefined,
  body: {},
  params: {},
  query: { redirectUri: 'http://localhost:3001' },
  level: 'error',
  timestamp: '2025-06-25 17:31:46'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:31:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:32:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:32:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Failed to connect to Redis:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 17:32:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:115:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQAS_iV7NOJo_tCiaowxNmmN0pSGFlL0SvntCWC-96w_XgXoVitw7EnqlUX3FfYeK04n9rX-sQ6LLAEw_pYpTzj8fC5gE82fVoSdZW_CafbxxXK6QsXz-lUDFM8K1JHB8w0Shhj7ssNKmNQHJ6KK5XYDD1yCzAOK1AZReuJv8TvRWhtZpyjZsGMwuT6pkBlDmzfTKmVG1XNJKVqqJbNV9WPNq7PWf2pQA30ftXbKkhmrgjzVj-HMWJWiI9XUPQIGHTze1j73HMenS8VCxHtL82fU0WP688UVXDOk-ds82InlUuaXfyhz__JPtxJOm7mscuwTCMtIfCQUrhV9t5OsTR7Klt0QgrsZTynsif9qtL1N_7MFkixVCGsXPCro44gkRwOzjEQC4okWzzTUUwNnmVlg',
    state: '7725da2de3ac835210975689b0561035aa60f628b4347a5bce3d5300018c12bb',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 17:33:45'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  message: 'Application Error Validation failed',
  stack: 'Error: Validation failed\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\routes\\facebook-public.js:115:11\n' +
    '    at C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\middleware\\errorHandler.js:188:21\n' +
    '    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\layer.js:95:5)\n' +
    '    at next (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\router\\route.js:149:13)\n' +
    '    at middleware (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  url: '/api/v1/facebook-public/oauth-callback',
  method: 'POST',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  body: {
    code: 'AQDkU2DQi8yL0lp-8P12S7TCoM15XG7qwDRh_AvEM7cNEf8G0nbYvVRC0EjReMK6FugdNnOMafjaZ49J5jd80afcxNBMlL2tS-e_z6K5QJOB3ikJEFZ8N06GhUVZa0FvmhoGddH16r2xDyCGZZlp7O0naMeIsvWA_zC3685eb8BeOSOq0_t1rSOibtqtCmlvyFhKqJjuwC5niqntG-T_x9MCcV_NcXZqIDGYmhXumcHBvY6z2hKrYcVbTnqK2xIxeiOjI9Qq2vR00Tfda_Nga8ldH0G8ctsZ0OiVmWz1Q_El0f3O1UCnyK8ipJQpRGhUwFCRvu7ntrVnRtLv_9CWq1cuxvXxyIkkdppZDBziGGftfpxZHAmt-oTnLe3diWOgMjFmceUJv0WhqQYwO9OjpB3R',
    state: '791016d9ee0e769ce38730835e116229f8ae58455ff9ef1b726116d055460921',
    redirectUri: 'http://localhost:3001/'
  },
  params: {},
  query: {},
  level: 'error',
  timestamp: '2025-06-25 17:34:22'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:28'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:29'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:30'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:30'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:31'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:31'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:32'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:32'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:33'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:33'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:34'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:34'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:35'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:35'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:36'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:36'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:37'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:37'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:38'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:39'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:40'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:41'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:41'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:42'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:42'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:43'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:44'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:44'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:45'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:45'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:46'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:46'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:47'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:47'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:48'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:48'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:49'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:50'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:50'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:51'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:51'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:52'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:53'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:54'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:54'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:55'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:55'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:56'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:56'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:57'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:57'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:58'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:18:59'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:00'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:01'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:02'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:02'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:03'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:03'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:04'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:04'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:05'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:05'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:06'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:06'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:07'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:07'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:08'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:08'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:09'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:10'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:10'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:11'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:11'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:12'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:12'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:13'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:13'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:14'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:15'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:16'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:17'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:18'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:18'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:19'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  code: 'ECONNREFUSED',
  level: 'error',
  message: 'Redis Client Error:',
  stack: 'AggregateError [ECONNREFUSED]: \n' +
    '    at internalConnectMultiple (node:net:1139:18)\n' +
    '    at afterConnectMultiple (node:net:1712:7)',
  timestamp: '2025-06-25 18:19:19'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  level: 'error',
  message: 'Failed to initialize server: app.use() requires a middleware function',
  stack: 'TypeError: app.use() requires a middleware function\n' +
    '    at Function.use (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\application.js:217:11)\n' +
    '    at PressureMaxServer.setupErrorHandling (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:221:14)\n' +
    '    at PressureMaxServer.initialize (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:76:12)\n' +
    '    at async PressureMaxServer.start (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:281:5)',
  timestamp: '2025-06-25 18:19:50'
}
{
  service: 'pressure-max-api',
  environment: 'development',
  level: 'error',
  message: 'Failed to initialize server: app.use() requires a middleware function',
  stack: 'TypeError: app.use() requires a middleware function\n' +
    '    at Function.use (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\node_modules\\express\\lib\\application.js:217:11)\n' +
    '    at PressureMaxServer.setupErrorHandling (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:221:14)\n' +
    '    at PressureMaxServer.initialize (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:76:12)\n' +
    '    at async PressureMaxServer.start (C:\\Users\\<USER>\\Desktop\\FB API SCRAPER\\pressure-max-api\\src\\server.js:281:5)',
  timestamp: '2025-06-25 18:22:20'
}
